plugins {
    id 'com.google.android.libraries.mapsplatform.secrets-gradle-plugin'
    id "com.android.application"
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
}

// Load keystore properties
def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file('key.properties')
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}

// Load local properties
def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

// Set Flutter version code and name
def flutterVersionCode = localProperties.getProperty('flutter.versionCode') ?: '1'
def flutterVersionName = localProperties.getProperty('flutter.versionName') ?: '1.0'

android {
    namespace "com.hbxn.homeess"
    compileSdkVersion 34
//    compileSdkVersion flutter.compileSdkVersion
    ndkVersion flutter.ndkVersion

    signingConfigs {
        jackery {
            storeFile file('../jackeryHome.jks')
            storePassword "7LQf0MuexL7UN1jj"
            keyAlias "key0"
            keyPassword "7LQf0MuexL7UN1jj"
            v1SigningEnabled true
            v2SigningEnabled true
        }
        geneverse {
            storeFile file('../geneverse.jks')
            storePassword "7LQf0MuexL7UN1jj"
            keyAlias "geneverse"
            keyPassword "7LQf0MuexL7UN1jj"
            v1SigningEnabled true
            v2SigningEnabled true
        }
    }

    // 多渠道Flavors配置
    productFlavors {
        jackery {
            resValue "string", "app_name", "Jackery Home"
            applicationId "com.hbxn.homeess"
            signingConfig signingConfigs.jackery
        }
        geneverse {
            resValue "string", "app_name", "Geneverse Home"
            applicationId "com.hbxn.geneverse"
            
            signingConfig signingConfigs.geneverse
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    
    packagingOptions {
        jniLibs {
            useLegacyPackaging true
        }
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    defaultConfig {
        multiDexEnabled true
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId "com.hbxn.homeess"
        // You can update the following values to match your application needs.
        // For more information, see: https://docs.flutter.dev/deployment/android#reviewing-the-gradle-build-configuration.
        minSdkVersion 24
        targetSdkVersion 35
        flavorDimensions "versionCode"
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
    }

    buildTypes {
        release {
            // TODO: Add your own signing config for the release build.
            // Signing with the debug keys for now, so `flutter run --release` works.
            signingConfig null
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
        debug {
            signingConfig null
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
//        release {
//            signingConfig signingConfigs.custom_keystore
//        }
    }
    println('------ get data from json start ------')
//    println(dartEnvVar.applicationId)
    println('------ get data from json end ------')
}

flutter {
    source '../..'
}

dependencies {
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.10"
    def multidex_version = "2.0.1"
    implementation "androidx.multidex:multidex:2.0.1"
    implementation("androidx.core:core-splashscreen:1.0.0")
}
