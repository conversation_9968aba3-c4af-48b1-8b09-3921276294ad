import 'package:flutter_basic/features/device_detail/accessories/ari_switch/routes/ari_switch_routes.dart';
import 'package:flutter_basic/features/device_detail/accessories/ct/routes/ct_page_route.dart';
import 'package:flutter_basic/features/device_detail/accessories/delay_switch/routes/delay_switch_page_route.dart';
import 'package:flutter_basic/features/device_detail/accessories/electricity_statistics/routes/page_route.dart';
import 'package:flutter_basic/features/device_detail/accessories/ev_charger/routes/ev_charger_page_route.dart';
import 'package:flutter_basic/features/device_detail/accessories/everhome_ecotracker/routes/everhome_ecotracker_page_route.dart';
import 'package:flutter_basic/features/device_detail/accessories/shelly_flood/routes/shelly_flood_page_route.dart';
import 'package:flutter_basic/features/device_detail/accessories/shelly_htg/routes/shelly_htg_page_route.dart';
import 'package:flutter_basic/features/device_detail/accessories/shelly_smoke/routes/shelly_smoke_page_route.dart';
import 'package:flutter_basic/features/device_detail/accessories/smart_socket/routes/smart_socket_page_route.dart';
import 'package:flutter_basic/features/device_detail/accessories/time_switch/time_switch_page_route.dart';
import 'package:flutter_basic/features/device_detail/alarm_fault/detail/alarm_fault_detail/routes/alarm_fault_detail_route.dart';
import 'package:flutter_basic/features/device_detail/alarm_fault/routes/alarm_fault_page_route.dart';
import 'package:flutter_basic/features/device_detail/epc_device_detail/routes/page_routes.dart';
import 'package:flutter_basic/features/device_detail/modify_device_name/routes/modify_device_name_routes.dart';
import 'package:flutter_basic/features/devices/add_smart_accessories/routes/add_smart_accessories_route.dart';
import 'package:flutter_basic/platform/platform.dart';
import 'package:flutter_basic/router/router.dart';

import '../../features/device_detail/bms_or_dc_machine_detail/routes/page_routes.dart';
import '../../features/device_detail/integrated_machine/routes/page_routes.dart';
import 'devices_routes_type.dart';

class IntergratedRoutes {
  static MaterialPageRoute onGenerateRoute(RouteSettings settings) {
    final routeName = settings.name ?? '';
    final arguments = settings.arguments;
    switch (routeName) {
      case IntergratedRoutesType.epcDeviceDetail:
        return MaterialPageRoute(
          builder: (context) => const EpcDeviceDetailPageRoute(),
          settings: settings,
        );
      case IntergratedRoutesType.intergratedMachine:
        return MaterialPageRoute(
          builder: (context) => const IntergratedPageRoute(),
          settings: settings,
        );
      case IntergratedRoutesType.ctDetail:
        return MaterialPageRoute(
          builder: (context) => const CtPageRoute(),
          settings: settings,
        );
      // 烟感
      case IntergratedRoutesType.shellySmoke:
        return MaterialPageRoute(
          builder: (context) => const ShellySmokeRoute(),
          settings: settings,
        );
      // 水浸
      case IntergratedRoutesType.shellyFlood:
        return MaterialPageRoute(
          builder: (context) => const ShellyFloodRoute(),
          settings: settings,
        );
      // 温湿度传感器
      case IntergratedRoutesType.shellyHtg:
        return MaterialPageRoute(
          builder: (context) => const ShellyHtgRoute(),
          settings: settings,
        );
      case IntergratedRoutesType.airSwitchDetail:
        return MaterialPageRoute(
          builder: (context) => const AriSwitchPageRoute(),
          settings: settings,
        );
      case IntergratedRoutesType.smartSocketDetail:
        return MaterialPageRoute(
          builder: (context) => const SmartSocketPageRoute(),
          settings: settings,
        );
      case IntergratedRoutesType.delaySwitchDetail:
        return MaterialPageRoute(
          builder: (context) => const DelaySwitchPageRoute(),
          settings: settings,
        );
      case IntergratedRoutesType.timeSwitchDetail:
        return MaterialPageRoute(
          builder: (context) => const TimeSwitchPageRoute(),
          settings: settings,
        );

      case IntergratedRoutesType.deviceAlarmAndFault:
        return MaterialPageRoute(
          builder: (context) => const AlarmFaultPageRoute(),
          settings: settings,
        );
      case IntergratedRoutesType.deviceAlarmAndFaultDetail:
        return MaterialPageRoute(
          builder: (context) => const AlarmFaultDetailPageRoute(),
          settings: settings,
        );
      case IntergratedRoutesType.modifyDeviceName:
        return MaterialPageRoute(
          builder: (context) => const ModifyDeviceNameRoute(),
          settings: settings,
        );
      case IntergratedRoutesType.bmsMachineDetail:
        return MaterialPageRoute(
          builder: (context) => const BmsMachinePageRoute(),
          settings: settings,
        );
      case IntergratedRoutesType.smartSocketDetailElectricity:
        return MaterialPageRoute(
          builder: (context) => const ElectricityStatisticsPageRoute(),
          settings: settings,
        );
      case IntergratedRoutesType.evChargerDetail:
        return MaterialPageRoute(
          builder: (context) => const EvChargerPageRoute(),
          settings: settings,
        );
      case IntergratedRoutesType.everHomeEcotracker:
        return MaterialPageRoute(
          builder: (context) => const EverHomeEcotrackerPageRoute(),
          settings: settings,
        );
      case IntergratedRoutesType.addSmartAccessories:
        return MaterialPageRoute(
          builder: (context) => const AddSmartAccessoriesRoute(),
          settings: settings,
        );
      default:
        return onUnknownRoute(settings);
    }
  }
}
