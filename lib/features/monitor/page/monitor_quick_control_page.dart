import 'dart:async';

import 'package:flutter_basic/components/custom_image_asset.dart';
import 'package:flutter_basic/components/custom_switch.dart';
import 'package:flutter_basic/event/local_mode_get_data_error_event.dart';
import 'package:flutter_basic/event/receive_energy_flow_event.dart';
import 'package:flutter_basic/features/devices/config/device_config.dart';
import 'package:flutter_basic/features/monitor/bloc/monitor_fetch_bloc.dart';
import 'package:flutter_basic/features/monitor/bloc/monitor_fetch_state.dart';
import 'package:flutter_basic/generated/l10n.dart';
import 'package:flutter_basic/platform/bluetooth/ble_exception/exception.dart';
import 'package:flutter_basic/platform/bluetooth/local_mode_manger.dart';
import 'package:flutter_basic/platform/community/local_mode_message_manager.dart';
import 'package:flutter_basic/platform/mqtt/mqtt_manager.dart';
import 'package:flutter_basic/platform/utils/combine.dart';
import 'package:flutter_basic/platform/utils/event_bus_manager.dart';
import 'package:flutter_basic/platform/utils/version.dart';
import 'package:flutter_basic/repositories/custom_exception/ServiceException.dart';
import 'package:flutter_basic/repositories/monitor_repository/model/model.dart';
import 'package:flutter_basic/repositories/monitor_repository/model/monitor_response_graph_model.dart';
import 'package:flutter_basic/repositories/monitor_repository/model/plug_switch.dart';
import 'package:flutter_basic/repositories/monitor_repository/model/top_charge.dart';
import 'package:flutter_basic/repositories/system_repository/system_client_repository.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../components/cus_InkWell/CusInkWell.dart';
import '../../../components/custom_dialog_box.dart';
import '../../../components/device_control_switch_item/SwitchItem.dart';
import '../../../platform/community/monitor/constants.dart';
import '../../../platform/community/monitor/quick_control_model_builder.dart';
import '../../../platform/platform.dart';
import '../../../platform/utils/color_utils.dart';
import '../../../repositories/combine_repository/model/combine_common_model.dart';
import '../../../repositories/monitor_repository/model/ups_switch.dart';
import '../../../repositories/system_repository/model/devices/ems_device_model.dart';
import '../bloc/monitor_fetch_event.dart';
import '../bloc/monitor_quick_control_bloc.dart';
import '../bloc/mqtt/mqtt_bloc.dart';

class QuickControlPage extends StatefulWidget {
  final String systemId;

  const QuickControlPage({super.key, required this.systemId});
  @override
  State<StatefulWidget> createState() {
    return _QuickControlPageState();
  }
}

class _QuickControlPageState extends State<QuickControlPage> {
  late MonitorQuickControlModel model;
  StreamSubscription? subscription;

  StreamSubscription? localModeErrorSubscription;

  /// 已经请求到模型
  bool isLoaded = false;

  /// 设备是否离线
  bool get isOffline =>
      model.emsProperty?.deviceStatus == DeviceStatus.OFFLINE.status;

  /// 是否渲染
  bool isReadyRender = true;
  @override
  void initState() {
    super.initState();
    final bloc = context.read<MonitorQuickControlBloc>();
    if (!LocalModeManger.instance.isLocalModeNow) {
      bloc.add(MonitorQuickControlFetched(widget.systemId));
    } else {
      bloc.add(MonitorQuickControlLocalModeFetchStart());
    }
    subscription =
        EventBusManager.eventBus.on<ReceiveEnergyFlowEvent>().listen((event) {
      if (!mounted) return;
      if (!isReadyRender) {
        return;
      }
      if (!LocalModeManger.instance.isLocalModeNow && !isLoaded) return;
      final bloc = context.read<MonitorQuickControlBloc>();
      model =
          getModelByMonitor(event.model, quickModel: isLoaded ? model : null);
      bloc.add(MonitorQuickControlLocalModeFetched(model));
      logger.d("ReceiveEnergyFlowEvent event ${model.toJson().toString()}");
      if (isCombine()) {
        _publishDataForDevices();
        context
            .read<MonitorFetchBloc>()
            .add(MonitorInfoFetched(widget.systemId));
      }
      setState(() {});
    });
    localModeErrorSubscription = EventBusManager.eventBus
        .on<LocalModeGetDataErrorEvent>()
        .listen((event) {
      if (!mounted) return;
      if (event.exception is BleNotConnectedException) {
        CustomLoading.dismissLoading();
        CustomToast.showToast(
            context, S.current.text('diyNew.bluetooth_is_disconnect'));
      }
      // 只提示一次
      localModeErrorSubscription?.cancel();
    });

    if (LocalModeManger.instance.isLocalModeNow) {
      LocalModeMessageManager.instance
          .setCurrentMessageType(LocalModelPackageType.package6);
      // 设置完上面召唤数据类型后，需要等待5秒的计时器到点执行，会造成先显示旧数据，过几秒才页面刷新
      // 所以这里手动召唤一次
      LocalModeManger.instance.sendPackageData(
          type: LocalModelPackageType.package6, isDelay: false);
    }
  }

  @override
  void dispose() {
    super.dispose();
    if (subscription != null) {
      subscription?.cancel();
    }
    localModeErrorSubscription?.cancel();
  }

  @override
  Widget build(BuildContext context) {
    final bloc = context.read<MonitorQuickControlBloc>();
    return BlocConsumer<MonitorQuickControlBloc, MonitorQuickControlState>(
      bloc: bloc,
      builder: (BuildContext context, state) {
        Widget content = Container(color: ColorsUtil.backgroundColor);
        if (state is MonitorQuickControlSuccess) {
          model = state.model;
          if (!isLoaded) {
            Map arguments = ModalRoute.of(context)!.settings.arguments as Map;
            if (arguments['model'] is MonitorResponseModel) {
              model = getModelByMonitor(arguments['model'], quickModel: model);
            }
            CustomLoading.dismissLoading();
          }
          isLoaded = true;
          content = _getQuickSuccessPage(context);
        } else if (state is MonitorQuickControlInProgress) {
          CustomLoading.showLoading(null);
        } else if (state is MonitorQuickControlFailure) {
          CustomLoading.dismissLoading();
        }
        return AppSnackBarWidget(
          child: Scaffold(
            backgroundColor: ColorsUtil.backgroundColor,
            appBar: CustomAppBar(
              backgroundColor: ColorsUtil.titleBarBackgroundColor,
              titleText: S.current.text('systemMonitor.Quick_Control'),
            ),
            body: content,
          ),
        );
      },
      listener: (BuildContext context, state) {},
    );
  }

  _getQuickSuccessPage(BuildContext context) {
    return BlocBuilder<MonitorFetchBloc, MonitorFetchState>(
        builder: (context, state) {
      final energyFlowChartVO = context
          .read<MonitorFetchBloc>()
          .state
          .monitorModel
          ?.energyFlowChartVO;
      final dcList = energyFlowChartVO?.getAllDCList();
      return SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            // _getUpsSwitch(upsSwitch),
            // _getPlugSwitch(plugSwitch),
            // model.emsProperty != null ? _buildQuickChargeWidget() : Container(),
            model.emsProperty != null ? _buildACOutputWidget() : Container(),
            dcList != null && dcList.isNotEmpty
                ? _buildDCOutputWidget(dcList)
                : Container(),
            model.plugSwitch != null && model.plugSwitch!.isNotEmpty
                ? _buildSmartSocketWidget()
                : Container(),
            model.emsProperty != null
                ? _buildPowerWidget(context)
                : Container(),
          ],
        ),
      );
    });
  }

  _buildQuickChargeWidget() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding:
              EdgeInsets.only(left: 20.w, right: 20.w, top: 10.h, bottom: 10.h),
          child: Text(
            S.current.text('systemMonitor.Quick_Charge'),
            style: TextStyle(
              fontSize: 14.sp,
              color: ColorsUtil.contentColor,
            ),
          ),
        ),
        Padding(
          padding: EdgeInsets.only(left: 20.w, right: 20.w),
          child: SwitchItem(
            iconName: 'icon_quick_control_lightning',
            name: S.current.text('systemMonitor.Quick_Charge'),
            onTap: _switchQuickCharge,
            switchState: model.emsProperty?.workModeValue == '3',
          ),
        ),
      ],
    );
  }

  // 是否要显示自动待机
  _showAutoStandby() {
    // 是二代 DIY
    final factoryStr = context
            .read<MonitorFetchBloc>()
            .state
            .monitorModel
            ?.systemVO
            ?.factoryModel ??
        "";
    final softVar = context
            .read<MqttBloc>()
            .deviceList
            .firstWhere((element) => element.productKey == 'HB-EMS')
            .softVer ??
        "";
    return isDIY2(factoryStr) && isGreaterThanV19(softVar);
  }

  _buildAutoStandbyWidget() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.only(left: 20.w, right: 20.w),
          child: Container(
            decoration: BoxDecoration(
              color: ColorsUtil.aboutCardColor,
              borderRadius: BorderRadius.circular(8.w),
            ),
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.all(Radius.circular(12.r)),
                    color: ColorsUtil.quickControlIconBgColor,
                  ),
                  width: 24.r,
                  height: 24.r,
                  child: CustomImageAsset(
                    'icon_quick_control_auto_standby',
                    width: 24,
                    height: 24,
                  ),
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        S.current.text('systemMonitor.auto_standby'),
                        style: TextStyle(
                          fontSize: 13.sp,
                          color: ColorsUtil.textColor,
                        ),
                      ),
                      SizedBox(height: 4.h),
                      Text(
                        S.current
                            .text('systemMonitor.auto_standby_on_description'),
                        style: TextStyle(
                          fontSize: 11.sp,
                          color: ColorsUtil.hintColor,
                        ),
                      ),
                      Text(
                        S.current
                            .text('systemMonitor.auto_standby_off_description'),
                        style: TextStyle(
                          fontSize: 11.sp,
                          color: ColorsUtil.hintColor,
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(width: 12.w),
                CustomSwitch(
                  value: _isAutoStandbyEnabled(),
                  disabled: isOffline,
                  onToggle: _switchAutoStandby,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// 获取能流图VO对象
  EnergyFlowChartVo? _getEnergyFlowChartVO() {
    return context
        .read<MonitorFetchBloc>()
        .state
        .monitorModel
        ?.energyFlowChartVO;
  }

  /// 获取使能配置值
  int? _getEnableConfig() {
    return _getEnergyFlowChartVO()?.enableConfig;
  }

  /// 检查指定位是否启用
  /// 返回 null 表示数据未加载完成
  bool? _isBitEnabled(int bitPosition) {
    final enableConfig = _getEnableConfig();
    logger.d('enableConfig: $enableConfig, bitPosition: $bitPosition'); // 调试信息
    if (enableConfig == null) return null; // 数据未加载完成
    return (enableConfig & (1 << bitPosition)) !=
        EnableConfigType.disable.value;
  }

  /// 设置指定位的值
  int _setBitValue(int enableConfig, int bitPosition, bool enabled) {
    return enabled
        ? enableConfig | (1 << bitPosition) // 设置位为1
        : enableConfig & ~(1 << bitPosition); // 设置位为0
  }

  bool _isAutoStandbyEnabled() {
    // bit 2 控制自动待机 ,1 使能， 0 不使能
    return _isBitEnabled(2) ?? false; // 数据未加载时默认为 false
  }

  Future<void> _switchAutoStandby(bool switchState) async {
    // 检查设备是否离线
    if (isOffline) {
      CustomToast.showToast(context, S.current.text('common.deviceOffline'));
      return;
    }
    try {
      final energyFlowChartVO = _getEnergyFlowChartVO();
      final enableConfig = _getEnableConfig();

      // 检查数据是否已加载
      if (enableConfig == null) {
        return;
      }

      // 计算新的使能配置值 (bit 2 控制自动待机)
      final newEnableConfig = _setBitValue(enableConfig, 2, switchState);

      await SystemClientRepository.instance.invoke(
          model.emsProperty?.deviceNo ?? '',
          model.emsProperty?.modelKey ?? '',
          "enableConfig" ?? '',
          model.emsProperty?.productKey ?? '',
          newEnableConfig);

      // 更新本地状态
      energyFlowChartVO?.enableConfig = newEnableConfig;
      setState(() {});

      // 显示成功提示
      if (mounted) {
        CustomToast.showToast(context, S.current.text('common.operateSuccess'));
      }
    } catch (_) {
      if (mounted) {
        CustomToast.showToast(context, S.current.text('common.operateFail'));
      }
      logger.e(_.toString());
    }
  }

  _buildACOutputWidget() {
    Widget acItem(EMSPropertyModel? emsModel) {
      return Padding(
        padding: EdgeInsets.only(left: 20.w, right: 20.w, bottom: 10.w),
        child: SwitchItem(
          iconName: 'monitor_ac',
          name: emsModel?.deviceName ?? S.current.text('common.unknownDevice'),
          onTap: (bool switchState) => _switchACOutput(emsModel, switchState),
          disabled: isOffline,
          switchState: emsModel?.acOutEnableValue == '$DIY_FLOW_SWITCH_OPEN',
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding:
              EdgeInsets.only(left: 20.w, right: 20.w, top: 10.w, bottom: 10.w),
          child: Text(
            S.current.text('systemMonitor.AC_OUTPUT'),
            style: TextStyle(
              fontSize: 14.sp,
              color: ColorsUtil.contentColor,
            ),
          ),
        ),
        acItem(model.emsProperty),
        ...?model.emsProperty?.slaverList?.map((element) => acItem(element)),
      ],
    );
  }

  Future<void> _publishDataForDevices() async {
    for (var acSnModel in _getDeviceSnList()) {
      MqttManager.instance.publishMessage(
        'data_get',
        acSnModel.sn,
        {
          "dev_list": [
            {
              "dev_sn": 'ems_${acSnModel.sn}',
              "meter_list": [
                diy_point_ac_output_switch,
                diy_point_ac_output_power,
              ]
            }
          ]
        },
        '${MqttDevicesConfig.publishClusterTopic}${acSnModel.sn}',
      );
    }
  }

  List<MasteredSnModel> _getDeviceSnList() {
    final slaverList = context
        .read<MonitorFetchBloc>()
        .state
        .monitorModel
        ?.energyFlowChartVO
        ?.acInfo
        ?.slaverList;
    return [
      MasteredSnModel(
        sn: context
                .read<MonitorFetchBloc>()
                .state
                .monitorModel
                ?.energyFlowChartVO
                ?.acInfo
                ?.deviceNo
                ?.replaceAll('ems_', '') ??
            '',
        mastered: Mastered.host,
      ),
      ...?slaverList?.map((element) => MasteredSnModel(
            sn: element.deviceNo?.replaceAll('ems_', '') ?? '',
            mastered: Mastered.client,
          )),
    ];
  }

  /// ac output 开关
  Future<void> _switchACOutput(
      EMSPropertyModel? emsModel, bool switchState) async {
    if (emsModel == null) return;
    logger.e('switchState ${switchState}');
    // 判断设备是否待机
    int deviceStatus = context
            .read<MonitorFetchBloc>()
            .state
            .monitorModel
            ?.energyFlowChartVO
            ?.emsGwVo
            ?.deviceStatus ??
        DeviceWorkStatus.normal.value!;
    if (deviceStatus == DeviceWorkStatus.standby.value ||
        deviceStatus == DeviceWorkStatus.eco.value) {
      CustomToast.showToast(context,
          S.current.text('business.device_work_mode_set_not_permitted'));
      return;
    }
    try {
      /// 打开传递1 关闭传0
      await SystemClientRepository.instance.invoke(
        emsModel.deviceNo ?? '',
        emsModel.modelKey ?? '',
        emsModel.acOutEnableIdentifier ?? '',
        emsModel.productKey ?? '',
        switchState ? DIY_FLOW_SWITCH_CLOSE : DIY_FLOW_SWITCH_OPEN,
      );
      emsModel.acOutEnableValue =
          '${switchState ? DIY_FLOW_SWITCH_CLOSE : DIY_FLOW_SWITCH_OPEN}';
      setState(() {});
    } catch (_) {
      if (mounted) {
        var msg = _ is ServiceException ? _.msg : null;
        CustomToast.showToast(context, $t(msg ?? 'common.operateFail'));
      }
      logger.d(_.toString());
    }
  }

  _buildDCOutputWidget(List<DcInfo?> dcList) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.only(left: 20.w, right: 20.w, top: 10.w),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                S.current.text('systemMonitor.UPS_Switch'),
                style: TextStyle(
                  fontSize: 14.sp,
                  color: ColorsUtil.contentColor,
                ),
              )
            ],
          ),
        ),
        ...dcList
            .map((item) => Column(
                  children: [
                    SizedBox(height: 10.h),
                    Padding(
                      padding: EdgeInsets.only(left: 20.w, right: 20.w),
                      child: SwitchItem(
                        iconName: 'monitor_dialog_dc_output_item_usb_icon',
                        name: item?.deviceName ?? '',
                        disabled: item!.disable,
                        onTap: (bool switchState) async {
                          await item.invokeDCSwitch(item, context);
                          setState(() {});
                        },
                        switchState: item.switchStatus,
                      ),
                    ),
                  ],
                ))
            .toList()
      ],
    );
  }

  _buildSmartSocketWidget() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.only(left: 20.w, right: 20.w, top: 20.h),
          child: Text(
            S.current.text('systemMonitor.quick_control_Smart_Socket'),
            style: TextStyle(
              fontSize: 14.sp,
              color: ColorsUtil.contentColor,
            ),
          ),
        ),
        ...model.plugSwitch!
            .map(
              (plug) => Padding(
                padding: EdgeInsets.only(left: 20.w, right: 20.w, top: 11.h),
                child: SwitchItem(
                  iconName: 'monitor_dialog_smart_plug_icon',
                  disabled: isOffline ||
                      plug.deviceStatus == DeviceStatus.OFFLINE.status,
                  name: plug.deviceName ?? '',
                  onTap: (bool switchState) => _switchSmartSocket(plug),
                  switchState: plug.switchStatus == '$DIY_FLOW_SWITCH_OPEN',
                ),
              ),
            )
            .toList(),
      ],
    );
  }

  // Power
  _buildPowerWidget(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding:
              EdgeInsets.only(left: 20.w, right: 20.w, top: 21.h, bottom: 10.h),
          child: Text(
            S.current.text('systemMonitor.Quick_Control_Power'),
            style: TextStyle(
              fontSize: 14.sp,
              color: ColorsUtil.contentColor,
            ),
          ),
        ),
        if (_showAutoStandby()) _buildAutoStandbyWidget(),
        if (_showAutoStandby()) SizedBox(height: 10.h),
        if (model.emsProperty?.turnOffValue == OnOffStatus.on.value &&
            model.emsProperty?.deviceWorkStatus != DeviceWorkStatus.eco)
          Padding(
            padding: EdgeInsets.only(left: 20.w, right: 20.w, bottom: 10.h),
            child: CusInkWell(
              onTap: isOffline
                  ? null
                  : () => _showDialog(
                        context: context,
                        title:
                            'systemMonitor.quick_device_restart_dialog_title',
                        content:
                            'systemMonitor.quick_device_restart_dialog_content',
                        okText: 'systemMonitor.quick_device_restart_dialog_ok',
                        onTap: () async {
                          var res = await _systemPowerControl(
                            context,
                            model.emsProperty!.deviceRebootIdentifier,
                          );
                          if (res && context.mounted) {
                            CustomToast.showToast(context,
                                S.current.text('common.operateSuccess'));
                            MqttManager.instance.setIsOnline(false);
                          }
                          isReadyRender = false;
                          await Future.delayed(const Duration(seconds: 2));
                          CustomLoading.showLoading("");
                          await Future.delayed(const Duration(seconds: 60));
                          CustomLoading.dismissLoading();
                          isReadyRender = true;
                        },
                      ),
              child: QuickControlListItem(
                data: ControlItem(
                  disabled: isOffline,
                  icon: CustomImageAsset(
                    'icon_device_restart',
                    width: 24,
                    height: 24,
                  ),
                  title: S.current.text('systemMonitor.quick_device_restart'),
                  type: ControlItemType.restart,
                ),
              ),
            ),
          ),
        if (model.emsProperty?.turnOffValue == OnOffStatus.on.value &&
            model.emsProperty?.deviceWorkStatus != DeviceWorkStatus.eco)
          // 待机
          Padding(
            padding: EdgeInsets.only(left: 20.w, right: 20.w, bottom: 10.h),
            child: CusInkWell(
              onTap: isOffline
                  ? null
                  : () => _showDialog(
                        context: context,
                        title: 'systemMonitor.Quick_Control_Power_dialog_title',
                        content:
                            'systemMonitor.Quick_Control_Power_dialog_content',
                        okText:
                            'systemMonitor.Quick_Control_Power_dialog_right_btn',
                        onTap: () async {
                          var res = await _systemPowerControl(
                              context,
                              model.emsProperty?.turnOffIdentifier ?? '',
                              1,
                              false);
                          if (!res) return;
                          isReadyRender = false;
                          if (isCombine()) {
                            await Future.delayed(const Duration(seconds: 10));
                          } else {
                            await Future.delayed(const Duration(seconds: 5));
                          }
                          CustomLoading.dismissLoading();
                          isReadyRender = true;
                        },
                      ),
              child: QuickControlListItem(
                data: ControlItem(
                  icon: CustomImageAsset(
                    'icon_quick_control_power',
                    width: 24,
                    height: 24,
                  ),
                  disabled: isOffline,
                  title: S.current.text('systemMonitor.Turn_Off_System'),
                  type: ControlItemType.turnOff,
                ),
              ),
            ),
          ),
        if (model.emsProperty?.turnOffValue == OnOffStatus.off.value ||
            model.emsProperty?.deviceWorkStatus == DeviceWorkStatus.eco)
          // 退出待机
          Padding(
            padding: EdgeInsets.only(left: 20.w, right: 20.w, bottom: 10.h),
            child: CusInkWell(
              onTap: isOffline
                  ? null
                  : () => _showDialog(
                        context: context,
                        title:
                            'systemMonitor.quick_device_power_on_dialog_title',
                        content:
                            'systemMonitor.quick_device_power_on_dialog_content',
                        okText: 'systemMonitor.quick_device_power_on_dialog_ok',
                        onTap: () async {
                          await _systemPowerControl(
                              context,
                              model.emsProperty?.turnOffIdentifier ?? "",
                              2,
                              false);
                          isReadyRender = false;
                          if (isCombine()) {
                            await Future.delayed(const Duration(seconds: 10));
                          } else {
                            await Future.delayed(const Duration(seconds: 5));
                          }
                          CustomLoading.dismissLoading();

                          isReadyRender = true;
                        },
                      ),
              child: QuickControlListItem(
                data: ControlItem(
                  icon: CustomImageAsset(
                    'icon_quick_control_power',
                    width: 24,
                    height: 24,
                  ),
                  disabled: isOffline,
                  title: S.current.text('systemMonitor.quick_device_power_on'),
                  type: ControlItemType.powerOn,
                ),
              ),
            ),
          ),
      ],
    );
  }

  _powerControlLoading(String value) {
    if (model.emsProperty?.turnOffValue == value) {
      return CustomLoading.dismissLoading();
    }
    // CustomLoading.showLoading(null);
    var startTime = DateTime.now();
    Timer.periodic(const Duration(milliseconds: 300), (timer) {
      if (!mounted) return;
      var time = DateTime.now().difference(startTime);
      if (model.emsProperty?.turnOffValue == value || time.inSeconds >= 15) {
        CustomLoading.dismissLoading();
        timer.cancel();
      }
    });
  }

  void _showDialog({
    required BuildContext context,
    required String title,
    required String content,
    required String okText,
    required Function() onTap,
  }) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext _) {
        return CustomInputDialogBox(
          title: S.current.text(title),
          content: S.current.text(content),
          actions: [
            CustomInputButton(
              text: S.current.text('common.cancel'),
              textColor: ColorsUtil.hintColor,
              onTap: () => Navigator.of(context).pop(),
            ),
            CustomInputButton(
              text: S.current.text(okText),
              textColor: ColorsUtil.highlightTextColor,
              onTap: () {
                Navigator.of(_).pop();
                Timer(const Duration(milliseconds: 300), onTap);
              },
            )
          ],
        );
      },
    );
  }

  /// 设备电源控制
  /// [value]  1 关机 2 开机 重启不用传值 给的默认值 在重启时用不上的
  Future<bool> _systemPowerControl(BuildContext _, String identify,
      [int value = DIY_FLOW_SWITCH_OPEN, bool closeLoading = true]) async {
    try {
      CustomLoading.showLoading("");

      /// 关机 传 1
      await SystemClientRepository.instance.invoke(
          model.emsProperty?.deviceNo ?? '',
          model.emsProperty?.modelKey ?? '',
          identify,
          model.emsProperty?.productKey ?? '',
          value);
      return true;
    } catch (_) {
      var title = 'common.operateFail';
      if (_ is ServiceException) title = _.msg ?? 'common.operateFail';
      if (mounted) {
        CustomToast.showToast(context, S.current.text(title));
      }
      logger.e(_.toString());
      return false;
    } finally {
      if (closeLoading) CustomLoading.dismissLoading();
    }
  }

  /// QuickCharge 开关
  Future<void> _switchQuickCharge(bool switchState) async {
    try {
      /// 打开传递3 关闭传2
      await SystemClientRepository.instance.invoke(
          model.emsProperty?.deviceNo ?? '',
          model.emsProperty?.modelKey ?? '',
          model.emsProperty?.workModeIdentifier ?? '',
          model.emsProperty?.productKey ?? '',
          switchState ? 2 : 3);
      model.emsProperty?.workModeValue = switchState ? '2' : '3';
      setState(() {});
    } catch (_) {
      if (mounted) {
        final msg = _ is ServiceException ? _.msg : null;
        CustomToast.showToast(context, $t(msg ?? 'common.operateFail'));
      }
      logger.d(_.toString());
    }
  }

  Future<void> _switchSmartSocket(PlugSwitch item) async {
    try {
      /// 打开传递1 关闭传0
      await SystemClientRepository.instance.invoke(
        item.deviceNo ?? '',
        item.modelKey ?? '',
        item.devCtrlInvokeIdentifier ?? '',
        item.productKey ?? '',
        item.switchStatus == '$DIY_FLOW_SWITCH_OPEN'
            ? DIY_FLOW_SWITCH_CLOSE
            : DIY_FLOW_SWITCH_OPEN,
      );
      item.switchStatus =
          '${item.switchStatus == '$DIY_FLOW_SWITCH_OPEN' ? DIY_FLOW_SWITCH_CLOSE : DIY_FLOW_SWITCH_OPEN}';
      if (LocalModeManger.instance.isLocalModeNow) {
        await Future.delayed(const Duration(seconds: 1));
        await LocalModeManger.instance.getPlugPackageData();
      } else {
        MqttManager.instance.diyDeviceGet();
        await Future.delayed(const Duration(seconds: 3));
      }
      setState(() {});
    } catch (_) {
      if (mounted) {
        final msg = _ is ServiceException ? _.msg : null;
        CustomToast.showToast(context, $t(msg ?? 'common.operateFail'));
      }
      logger.d(_.toString());
    }
  }
}

// 下面的数据都是在别的地方引用的 不属于这个页面业务
// enum MonitorType { normal, warn, error }
class ControlItems {
  final String? title;
  final List<ControlItem>? data;

  ControlItems({
    this.title,
    this.data,
  });

  static empty() {
    return ControlItems();
  }

  static ControlItems plugSwitchToControlItems(List<PlugSwitch> plugSwitchs) {
    if (plugSwitchs.isEmpty) {
      return ControlItems.empty();
    }
    return ControlItems(
      title: plugSwitchs[0].deviceNo,
      data: plugSwitchs
          .map(
            (e) => ControlItem.plugSwitchToControlItem(
              plugSwitch: e,
              icon: CustomImageAsset(
                'icon_quick_control_socket',
                width: 24,
                height: 24,
              ),
              unit: 'w',
            ),
          )
          .toList(),
    );
  }

  static ControlItems upsSwitchToControlItems(List<UpsSwitch> upsSwitchs) {
    if (upsSwitchs.isEmpty) {
      return ControlItems.empty();
    }
    return ControlItems(
      title: upsSwitchs[0].deviceNo,
      data: upsSwitchs
          .map(
            (e) => ControlItem.upsSwitchToControlItem(
              upsSwitch: e,
              icon: CustomImageAsset(
                'icon_circuit_breaker',
                width: 24,
                height: 24,
              ),
            ),
          )
          .toList(),
    );
  }

  static ControlItems topChargeToControlItems(List<TopCharge> topCharges) {
    if (topCharges.isEmpty) {
      return ControlItems.empty();
    }
    return ControlItems(
      title: topCharges[0].deviceNo,
      data: topCharges
          .map(
            (e) => ControlItem.topChargeToControlItem(
              topCharge: e,
              icon: CustomImageAsset(
                'icon_quick_control_lightning',
                width: 24,
                height: 24,
              ),
            ),
          )
          .toList(),
    );
  }
}

enum ControlItemType {
  topCharge,
  upsSwitch,
  plugSwitch,
  turnOff,
  restart,
  powerOn
}

class ControlItem {
  final Widget? icon;
  final String? title;
  final String? unit;
  String? switchStatus;
  final String? modelKey;
  final String? identify;
  final String? productKey;
  final String? deviceNo;
  final double? socketPower;

  // final MonitorType? type;
  final ControlItemType? type;
  final List<Widget>? contents;

  final bool disabled;

  ControlItem({
    this.icon,
    this.title,
    this.unit,
    this.switchStatus,
    this.socketPower,
    this.modelKey,
    this.identify,
    this.productKey,
    // this.type,
    this.type,
    this.contents,
    this.deviceNo,
    this.disabled = false,
  });

  static empty() {
    return ControlItem();
  }

  static ControlItem plugSwitchToControlItem(
      {required PlugSwitch plugSwitch, Widget? icon, String? unit}) {
    return ControlItem(
      icon: icon,
      type: ControlItemType.plugSwitch,
      deviceNo: plugSwitch.deviceNo,
      title: plugSwitch.deviceName,
      switchStatus: plugSwitch.switchStatus,
      socketPower: plugSwitch.socketPower,
      unit: unit,
      modelKey: plugSwitch.modelKey,
      identify: plugSwitch.devCtrlInvokeIdentifier,
      productKey: plugSwitch.productKey,
      contents: [
        plugSwitch.latestJobTime != null
            ? Padding(
                padding: EdgeInsets.only(right: 4.w),
                child: Text(
                  plugSwitch.latestJobTime ?? '',
                  style: TextStyle(
                    fontSize: 13.sp,
                    color: ColorsUtil.connectedTextColor,
                  ),
                ),
              )
            : const SizedBox(),
        plugSwitch.socketPower != null
            ? Padding(
                padding: EdgeInsets.only(left: 4.w),
                child: Text(
                  '${plugSwitch.socketPower}${unit ?? ''}',
                  style: TextStyle(
                    fontSize: 13.sp,
                    color: ColorsUtil.hintColor,
                  ),
                ),
              )
            : const SizedBox(),
      ],
    );
  }

  static ControlItem upsSwitchToControlItem(
      {required UpsSwitch upsSwitch, Widget? icon, String? unit}) {
    return ControlItem(
      type: ControlItemType.upsSwitch,
      icon: icon,
      // title: upsSwitch.propertyName,
      switchStatus: upsSwitch.propertyValue,
      unit: unit,
      modelKey: upsSwitch.modelKey,
      identify: upsSwitch.upsDCLoadSwitchInvokeIndetify,
      productKey: upsSwitch.productKey,
      deviceNo: upsSwitch.deviceNo,
      contents: [
        upsSwitch.propertyName != null
            ? Text(
                upsSwitch.propertyName ?? '',
                style: TextStyle(
                  fontSize: 13.sp,
                  color: ColorsUtil.textColor,
                ),
              )
            : const SizedBox(),
      ],
    );
  }

  static ControlItem topChargeToControlItem(
      {required TopCharge topCharge, Widget? icon, String? unit}) {
    return ControlItem(
      type: ControlItemType.topCharge,
      icon: icon,
      title: topCharge.deviceName,
      switchStatus: topCharge.propertyValue,
      unit: unit,
      modelKey: topCharge.modelKey,
      identify: topCharge.topChargeInvokeIdentify,
      productKey: topCharge.productKey,
      deviceNo: topCharge.deviceNo,
      contents: [],
    );
  }
}

class ControlCard extends StatefulWidget {
  final EdgeInsetsGeometry? margin;
  final ControlItem data;
  final bool isSelect;
  final Color? cardBgColor;

  const ControlCard(
      {super.key,
      required this.data,
      this.cardBgColor,
      this.margin,
      this.isSelect = false});

  @override
  State<StatefulWidget> createState() {
    return _ControlCardState();
  }
}

class _ControlCardState extends State<ControlCard> {
  @override
  Widget build(BuildContext context) {
    return Card(
      margin: widget.margin ?? EdgeInsets.zero,
      color: widget.cardBgColor ?? ColorsUtil.backgroundColor,
      shape: RoundedRectangleBorder(
        side: BorderSide(color: ColorsUtil.transparentColor),
        borderRadius: BorderRadius.all(
          Radius.circular(8.w),
        ),
      ),
      shadowColor: ColorsUtil.transparentColor,
      child: Container(
        // height: 75.h,
        width: double.infinity,
        padding:
            EdgeInsets.only(left: 10.w, right: 10.w, top: 10.h, bottom: 10.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Flex(
              direction: Axis.horizontal,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                widget.data.icon != null
                    ? Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.all(Radius.circular(12.r)),
                          color: ColorsUtil.quickControlIconBgColor,
                        ),
                        width: 24.r,
                        height: 24.r,
                        child: widget.data.icon,
                      )
                    : const SizedBox(),
                Expanded(
                  flex: 1,
                  child: Padding(
                    padding: EdgeInsets.only(left: 11.w, right: 5.w),
                    child: Text(
                      widget.data.title ?? '',
                      style: TextStyle(
                        fontSize: 13.sp,
                        color: ColorsUtil.textColor,
                      ),
                    ),
                  ),
                ),
                CustomSwitch(
                    value: widget.data.switchStatus == '1' ||
                        widget.data.switchStatus == '3',
                    onToggle: (flag) async {
                      try {
                        /// 打开传递1 关闭传0
                        await SystemClientRepository.instance.invoke(
                            widget.data.deviceNo ?? '',
                            widget.data.modelKey ?? '',
                            widget.data.identify ?? '',
                            widget.data.productKey ?? '',
                            flag
                                ? (widget.data.type == ControlItemType.topCharge
                                    ? 3
                                    : DIY_FLOW_SWITCH_OPEN)
                                : (widget.data.type == ControlItemType.topCharge
                                    ? 2
                                    : DIY_FLOW_SWITCH_CLOSE));
                        widget.data.switchStatus = flag
                            ? (widget.data.type == ControlItemType.topCharge
                                ? '3'
                                : '1')
                            : (widget.data.type == ControlItemType.topCharge
                                ? '2'
                                : '0');
                        setState(() {});
                      } catch (_) {
                        if (context.mounted) {
                          var msg = _ is ServiceException ? _.msg : null;
                          CustomToast.showToast(
                              context, $t(msg ?? 'common.operateFail'));
                        }
                        logger.d(_.toString());
                      }
                    }),
              ],
            ),
            widget.data.contents != null && widget.data.contents!.isNotEmpty
                ? Padding(
                    padding: EdgeInsets.only(top: 10.h),
                    child: Flex(
                      direction: Axis.horizontal,
                      children: _getContents(),
                    ),
                  )
                : const SizedBox()
          ],
        ),
      ),
    );
  }

  _getContents() {
    final contents = widget.data.contents;
    return List.generate(contents?.length ?? 0, (index) {
      if (contents == null || contents[index] is SizedBox) {
        return const SizedBox();
      }
      if (index == contents.length - 1) {
        return contents[index];
      }
      return Flex(
        direction: Axis.horizontal,
        children: [
          contents[index],
          Container(
            height: 2,
            width: 2,
            decoration: BoxDecoration(
              color: ColorsUtil.hintColor,
              borderRadius: const BorderRadius.all(Radius.circular(1)),
            ),
          )
        ],
      );
    });
  }
}

class QuickControlListItem extends StatelessWidget {
  final EdgeInsetsGeometry? margin;
  final ControlItem data;
  final isSelect = false;

  const QuickControlListItem({super.key, required this.data, this.margin});

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: margin ?? EdgeInsets.zero,
      color: ColorsUtil.aboutCardColor,
      shape: RoundedRectangleBorder(
        side: BorderSide(color: ColorsUtil.transparentColor),
        borderRadius: BorderRadius.all(
          Radius.circular(8.w),
        ),
      ),
      elevation: 0,
      shadowColor: ColorsUtil.transparentColor,
      child: Container(
        // height: 75.h,
        width: double.infinity,
        padding:
            EdgeInsets.only(left: 16.w, right: 14.w, top: 10.h, bottom: 10.h),
        child: Flex(
          direction: Axis.horizontal,
          children: [
            data.icon != null
                ? Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.all(Radius.circular(12.r)),
                      color: ColorsUtil.quickControlIconBgColor,
                    ),
                    width: 24.r,
                    height: 24.r,
                    child: data.icon,
                  )
                : const SizedBox(),
            Expanded(
              flex: 1,
              child: Padding(
                padding: EdgeInsets.only(left: 5.w, right: 5.w),
                child: Text(
                  data.title ?? '',
                  style: TextStyle(
                    fontSize: 13.sp,
                    color: ColorsUtil.textColor
                        .withAlpha(data.disabled ? 100 : 255),
                  ),
                ),
              ),
            ),
            CustomImageAsset(
              'arrow_right',
              width: 20.w,
              height: 20.h,
            )
          ],
        ),
      ),
    );
  }
}
