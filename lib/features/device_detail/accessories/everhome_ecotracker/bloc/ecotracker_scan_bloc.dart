import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:flutter_basic/platform/utils/log_utils.dart';

import '../model/ecotracker_device_model.dart';
import '../service/mdns_scan_service.dart';
import 'ecotracker_scan_event.dart';
import 'ecotracker_scan_state.dart';

/// 内部事件：设备列表更新
class _DevicesUpdatedEvent extends EcoTrackerScanEvent {
  final List<EcoTrackerDeviceModel> devices;

  const _DevicesUpdatedEvent(this.devices);

  @override
  List<Object?> get props => [devices];
}

/// 内部事件：扫描定时器超时
class _ScanTimerExpiredEvent extends EcoTrackerScanEvent {
  const _ScanTimerExpiredEvent();
}

/// 内部事件：扫描定时器计时
class _ScanTimerTickEvent extends EcoTrackerScanEvent {
  final int remainingTime;

  const _ScanTimerTickEvent(this.remainingTime);

  @override
  List<Object?> get props => [remainingTime];
}

class EcoTrackerScanBloc
    extends Bloc<EcoTrackerScanEvent, EcoTrackerScanState> {
  final MdnsScanService _scanService = MdnsScanService();
  StreamSubscription<List<EcoTrackerDeviceModel>>? _devicesSubscription;
  Timer? _scanTimer;

  EcoTrackerScanBloc() : super(const EcoTrackerScanState()) {
    on<StartScanEvent>(_onStartScan);
    on<StopScanEvent>(_onStopScan);
    on<RefreshScanEvent>(_onRefreshScan);
    on<ConnectDeviceEvent>(_onConnectDevice);
    on<ClearScanResultsEvent>(_onClearScanResults);
    on<_DevicesUpdatedEvent>(_onDevicesUpdated);
    on<_ScanTimerExpiredEvent>(_onScanTimerExpired);
    on<_ScanTimerTickEvent>(_onScanTimerTick);

    // 监听设备发现
    _devicesSubscription = _scanService.devicesStream.listen((devices) {
      if (!isClosed) {
        add(_DevicesUpdatedEvent(devices));
      }
    });
  }

  /// 处理设备列表更新
  Future<void> _onDevicesUpdated(
      _DevicesUpdatedEvent event, Emitter<EcoTrackerScanState> emit) async {
    logger.d('设备列表更新: 发现 ${event.devices.length} 个设备');
    for (var device in event.devices) {
      logger.d('设备详情: ${device.toString()}');
    }
    emit(state.copyWith(devices: event.devices));
  }

  /// 处理扫描定时器超时
  Future<void> _onScanTimerExpired(
      _ScanTimerExpiredEvent event, Emitter<EcoTrackerScanState> emit) async {
    emit(state.copyWith(
      scanStatus: ScanStatus.success,
      remainingTime: 0,
    ));
  }

  /// 处理扫描定时器计时
  Future<void> _onScanTimerTick(
      _ScanTimerTickEvent event, Emitter<EcoTrackerScanState> emit) async {
    emit(state.copyWith(remainingTime: event.remainingTime));
  }

  /// 开始扫描设备
  Future<void> _onStartScan(
      StartScanEvent event, Emitter<EcoTrackerScanState> emit) async {
    try {
      logger.d('开始扫描 EcoTracker 设备');

      // 清空之前的设备列表
      _scanService.clearDevices();

      emit(state.copyWith(
        scanStatus: ScanStatus.scanning,
        devices: [], // 清空设备列表
        error: null,
        remainingTime: state.scanDuration,
      ));

      // 启动扫描服务
      await _scanService.startScan(
        timeout: Duration(seconds: state.scanDuration),
      );

      // 启动倒计时定时器
      _startScanTimer(emit);
    } catch (e) {
      logger.e('启动扫描失败: $e');
      emit(state.copyWith(
        scanStatus: ScanStatus.failure,
        error: '启动扫描失败: $e',
      ));
    }
  }

  /// 停止扫描设备
  Future<void> _onStopScan(
      StopScanEvent event, Emitter<EcoTrackerScanState> emit) async {
    try {
      logger.d('停止扫描 EcoTracker 设备');

      _scanTimer?.cancel();
      _scanTimer = null;

      await _scanService.stopScan();

      emit(state.copyWith(
        scanStatus: ScanStatus.stopped,
        remainingTime: 0,
      ));
    } catch (e) {
      logger.e('停止扫描失败: $e');
      emit(state.copyWith(
        scanStatus: ScanStatus.failure,
        error: '停止扫描失败: $e',
      ));
    }
  }

  /// 刷新扫描
  Future<void> _onRefreshScan(
      RefreshScanEvent event, Emitter<EcoTrackerScanState> emit) async {
    // 清空当前结果
    _scanService.clearDevices();
    emit(state.copyWith(devices: []));

    // 重新开始扫描
    add(const StartScanEvent());
  }

  /// 连接设备
  Future<void> _onConnectDevice(
      ConnectDeviceEvent event, Emitter<EcoTrackerScanState> emit) async {
    try {
      logger.d('连接设备: ${event.deviceName} (${event.ipAddress}:${event.port})');

      emit(state.copyWith(
        connectStatus: ConnectStatus.connecting,
        error: null,
      ));

      // 这里可以添加实际的设备连接逻辑
      // 例如：HTTP 请求、WebSocket 连接等
      await _connectToDevice(event);

      emit(state.copyWith(
        connectStatus: ConnectStatus.connected,
        connectedDeviceId: event.deviceId,
      ));
    } catch (e) {
      logger.e('连接设备失败: $e');
      emit(state.copyWith(
        connectStatus: ConnectStatus.failed,
        error: '连接设备失败: $e',
      ));
    }
  }

  /// 清空扫描结果
  Future<void> _onClearScanResults(
      ClearScanResultsEvent event, Emitter<EcoTrackerScanState> emit) async {
    _scanService.clearDevices();
    emit(state.copyWith(
      devices: [],
      scanStatus: ScanStatus.initial,
      connectStatus: ConnectStatus.initial,
      connectedDeviceId: null,
      error: null,
    ));
  }

  /// 启动扫描倒计时定时器
  void _startScanTimer(Emitter<EcoTrackerScanState> emit) {
    _scanTimer?.cancel();

    _scanTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (isClosed) {
        timer.cancel();
        return;
      }

      // 使用 add 方法而不是直接 emit，避免在事件处理器外部调用 emit
      final remainingTime = state.remainingTime - 1;

      if (remainingTime <= 0) {
        timer.cancel();
        add(const _ScanTimerExpiredEvent());
        _scanService.stopScan();
      } else {
        add(_ScanTimerTickEvent(remainingTime));
      }
    });
  }

  /// 连接到设备的具体实现
  Future<void> _connectToDevice(ConnectDeviceEvent event) async {
    // 模拟连接延迟
    await Future.delayed(const Duration(seconds: 2));

    // 这里可以添加实际的连接逻辑
    // 例如：
    // - HTTP 请求验证设备
    // - 建立 WebSocket 连接
    // - 获取设备状态

    logger.d('设备连接成功: ${event.deviceName}');
  }

  @override
  Future<void> close() {
    _scanTimer?.cancel();
    _devicesSubscription?.cancel();
    _scanService.dispose();
    return super.close();
  }
}
