import 'package:flutter_basic/components/components.dart';
import 'package:flutter_basic/components/custom_card_list.dart';
import 'package:flutter_basic/components/custom_dialog_box.dart';
import 'package:flutter_basic/components/custom_switch.dart';
import 'package:flutter_basic/components/delete_button.dart';
import 'package:flutter_basic/event/refresh_devices_page_event.dart';
import 'package:flutter_basic/features/device_detail/accessories/ct/page/ct_energy_trend_page.dart';
import 'package:flutter_basic/features/device_detail/accessories/ct/page/phase_reverse_page.dart';
import 'package:flutter_basic/features/device_detail/accessories/smart_socket/page/modify_name_page.dart';
import 'package:flutter_basic/features/device_detail/components/device_notifacation_view.dart';
import 'package:flutter_basic/features/diy_new/accessories/model/accessorise_device_filter.dart';
import 'package:flutter_basic/features/diy_new/shelly/shelly_ct/page/shelly_ct_select_page.dart';
import 'package:flutter_basic/features/monitor/bloc/monitor_fetch_bloc.dart';
import 'package:flutter_basic/platform/bluetooth/local_mode_manger.dart';
import 'package:flutter_basic/platform/platform.dart';
import 'package:flutter_basic/platform/utils/color_utils.dart';
import 'package:flutter_basic/platform/utils/event_bus_manager.dart';
import 'package:flutter_basic/repositories/device_repository/model/device_ct_details_response.dart';
import 'package:flutter_basic/router/devices_feature_routes/devices_routes.dart';
import 'package:flutter_basic/router/devices_feature_routes/devices_routes_type.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';

import '../../../../../components/cus_InkWell/CusInkWell.dart';
import '../../../../../platform/utils/version.dart';
import '../../../../monitor/bloc/mqtt/mqtt_bloc.dart';
import '../bloc/ct_bloc.dart';

class CtPage extends StatefulWidget {
  const CtPage({super.key});

  @override
  State<StatefulWidget> createState() => _CtPageState();
}

class _CtPageState extends State<CtPage> {
  late final bloc = BlocProvider.of<CtBloc>(context);
  String refreshTime = '';

  @override
  void initState() {
    super.initState();
    DateFormat dateFormat = DateFormat('yyyy-MM-dd HH:mm:ss');
    refreshTime = dateFormat.format(DateTime.now());
    context.read<CtBloc>()
      ..add(GetCtDetailsEvent())
      ..add(GetWarningCountEvent());
  }

  /// 顶部警告通知图标
  Widget get warningNotification => BlocBuilder<CtBloc, CtState>(
        buildWhen: (previous, current) => current is GetWarningCountSuccess,
        builder: (context, state) {
          var messageCounts = 0;
          if (state is GetWarningCountSuccess) {
            messageCounts = state.count;
          }
          return DeviceNotificationView(
            messageCount: messageCounts,
            onTap: () {
              Navigator.of(context).push(
                IntergratedRoutes.onGenerateRoute(
                  RouteSettings(
                    name: IntergratedRoutesType.deviceAlarmAndFault,
                    arguments: {
                      'devicesNoList': [bloc.deviceSn]
                    },
                  ),
                ),
              );
            },
          );
        },
      );

  /// 工作设置
  Widget get workSetting => BlocConsumer<CtBloc, CtState>(
        listener: (context, state) {
          if (state is SetCtPhaseSuccess) {
            var msg = S.current.text('common.operateSuccess');
            CustomToast.showToast(context, msg);
          } else if (state is SetCtPhaseFailure) {
            CustomToast.showToast(context, S.current.text(state.message));
          }
        },
        buildWhen: (previous, current) =>
            current is GetCtDetailsSuccess || current is SetCtPhaseSuccess,
        builder: (context, state) {
          // 获取设备工厂型号以判断是否为二代DIY设备
          final factoryStr = context
                  .read<MonitorFetchBloc>()
                  .state
                  .monitorModel
                  ?.systemVO
                  ?.factoryModel ??
              "";

          // 获取设备工厂型号以判断是否为二代DIY设备
          final softVar = context
                  .read<MqttBloc>()
                  .deviceList
                  .firstWhere((element) => element.productKey == 'HB-EMS')
                  .softVer ??
              "";
          List<CardItemData> cardList = [];
          if (bloc.details?.deviceType == AccessoriesDeviceType.ct3) {
            cardList.add(_buildPhaseItem(context));
          } else if (bloc.details?.deviceType == AccessoriesDeviceType.ct) {
            cardList.add(_buildChannelItem(context));
          }
          if (isDIY2(factoryStr) && isGreaterThanV18(softVar)) {
            // 二代DIY设备且软件版本大于19
            // 不显示相位反转
          } else {
            if (bloc.details?.phaseReverseIdentifier != null) {
              cardList.add(_buildPhaseReverseItem(context));
            }
          }
          if (bloc.details?.lowModeSetPropertyIdentifier != null) {
            cardList.add(_buildEcoModeItem(context));
          }
          return CustomCardList(
            sectionTitle: S.current.text('device.ct_details_work_settings'),
            cardList: cardList,
          );
        },
      );

  /// 基本信息
  Widget get basicInfo => BlocConsumer<CtBloc, CtState>(
        listener: (_, state) {
          if (state is SetDeviceNameSuccess) {
            CustomToast.showToast(
                context, S.current.text('common.operateSuccess'));
          }
          if (state is SetDeviceNameFailure) {
            CustomToast.showToast(context, S.current.text(state.message));
          }
        },
        buildWhen: (previous, current) => current is GetCtDetailsSuccess,
        builder: (context, state) {
          var details = bloc.details;
          // 根据设备类型获取title的index
          // 多语言显示的内容以 / 分割
          int titleIndex =
              details?.deviceType == AccessoriesDeviceType.ct3 ? 1 : 0;
          return CustomCardList(
              sectionTitle: S.current.text('device.ct_details_basic_info'),
              cardList: [
                CardItemData(
                  title: S.current.text('device.Device_Name'),
                  onTap: _showEditName,
                  content: details?.deviceName ?? '-',
                ),
                ...details?.propertyVOList.map((e) => CardItemData(
                          title: S.current.text(e.name ?? ""),
                          showIcon: false,
                          content: _splitTitle(e.value ?? '-', titleIndex),
                        )) ??
                    []
              ]);
        },
      );

  /// 能量趋势
  Widget get nergyTrend {
    var viewButton = CusInkWell(
      onTap: () {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => CtEnergyTrendPage(deviceNo: bloc.deviceSn),
          ),
        );
      },
      child: Row(children: [
        Text(
          S.current.text('device.ct_details_energy_trend_view'),
          style: TextStyle(
              color: ColorsUtil.trendTabTextSelectedColor, fontSize: 12.sp),
        ),
        SizedBox(width: 2.w),
        Icon(
          Icons.arrow_forward_ios,
          color: ColorsUtil.trendTabTextSelectedColor,
          size: 10.sp,
        )
      ]),
    );
    return BlocBuilder<CtBloc, CtState>(
      buildWhen: (previous, current) => current is GetCtDetailsSuccess,
      builder: (context, state) {
        // 根据设备类型获取title的index
        // 多语言显示的内容以 / 分割
        var titleIndex =
            bloc.details?.deviceType == AccessoriesDeviceType.ct3 ? 1 : 0;

        return CustomCardList(
          sectionTitle: S.current.text('device.ct_details_energy_trend'),
          sectionRightWidget: viewButton,
          cardList: bloc.details?.energyMap.entries.map((e) {
                var title = S.current.text(e.key);
                if (title.contains('/')) title = title.split('/')[titleIndex];
                return CardItemData(
                  title: title,
                  showIcon: false,
                  content: '${e.value?.toStringAsFixed(2) ?? '-'}kWh',
                );
              }).toList() ??
              [],
        );
      },
    );
  }

  /// 监听一些全局状态
  Widget get listen => BlocListener<CtBloc, CtState>(
        listener: (BuildContext context, CtState state) {
          if (state is ShowLoading) {
            CustomLoading.showLoading(null);
          } else if (state is HideLoading) {
            CustomLoading.dismissLoading();
          } else if (state is GetCtDetailsSuccess) {
            setState(() {});
          } else if (state is GetCtDetailsFailure) {
            CustomToast.showToast(context, S.current.text(state.message));
          }
        },
        child: const SizedBox.shrink(),
      );

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: ColorsUtil.backgroundColor,
        appBar: CustomAppBar(
          title: AppBarCenterText(
            title: S.current.text('device.ct_details_page_title'),
          ),
          actions: [
            Padding(
              padding: const EdgeInsets.only(right: 20),
              child: warningNotification,
            )
          ],
        ),
        body: SafeArea(
          child: Column(
            children: [
              listen,
              if (bloc.details != null)
                Expanded(
                  child: SingleChildScrollView(
                    padding: EdgeInsets.only(
                        left: 20.w, right: 20.w, top: 10.w, bottom: 10.w),
                    child: Column(children: [
                      workSetting,
                      SizedBox(height: 10.w),
                      basicInfo,
                      SizedBox(height: 10.w),
                      if (!LocalModeManger.instance.isLocalModeNow) ...[
                        nergyTrend,
                        SizedBox(height: 10.w),
                      ],
                      DeleteButton(
                        margin: EdgeInsets.only(top: 20.w, bottom: 20.w),
                        text: S.current.text('device.ct_details_remove_device'),
                        onTap: _clickRemove,
                      ),
                      Text(
                        '${S.current.text('trend.intergrated_updated')} $refreshTime',
                        style: TextStyle(
                            color: ColorsUtil.assistTextColor, fontSize: 12.sp),
                      ),
                    ]),
                  ),
                ),
              SizedBox(height: 10.w),
              if (bloc.details != null)
                BlocListener<CtBloc, CtState>(
                  listener: (_, state) {
                    if (state is RemoveDeviceSuccess) {
                      EventBusManager.eventBus.fire(RefreshDevicesPageEvent());
                      CustomToast.showToast(
                          context, S.current.text('common.operateSuccess'));
                      Navigator.of(context).pop();
                    } else if (state is RemoveDeviceFailure) {
                      CustomToast.showToast(
                          context, S.current.text(state.message));
                    }
                  },
                  child: const SizedBox.shrink(),
                ),
            ],
          ),
        ));
  }

  /// 修改名称
  void _showEditName() => Navigator.of(context).push(
        MaterialPageRoute(builder: (context) {
          return ModifyNamePage(
            title: 'device.plug_device_name',
            value: bloc.details?.deviceName ?? "",
            onSubmitted: (v) => bloc.add(SetDeviceNameEvent(v)),
          );
        }),
      );

  //删除设备
  _clickRemove() => showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return CustomInputDialogBox(
          title: S.current.text('device.remove_device'),
          content: S.current.text('device_remove_device_alert_content'),
          actions: [
            CustomInputButton(
              text: S.current.text('common.cancel'),
              textColor: ColorsUtil.hintColor,
              onTap: () => Navigator.of(context).pop(),
            ),
            CustomInputButton(
              text: S.current.text('diyNew.confirm'),
              textColor: ColorsUtil.highlightTextColor,
              onTap: () {
                Navigator.of(context).pop();
                bloc.add(RemoveDeviceEvent());
              },
            )
          ],
        );
      });

  /// 相位显示
  CardItemData _buildPhaseItem(BuildContext context) {
    // 获取设备工厂型号以判断是否为二代DIY设备
    final factoryStr = context
            .read<MonitorFetchBloc>()
            .state
            .monitorModel
            ?.systemVO
            ?.factoryModel ??
        "";

    return CardItemData(
      title: S.current.text('device.ct_details_phase'),
      content: bloc.details?.phase?.translatedName ?? '-',
      onTap: () async {
        if (bloc.details?.isOffline == true) return;
        var value = await Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => ShellyCtPhaseSelectPage<ShellyCtPhaseType>(
              value: bloc.details?.phase,
              list: ShellyCtPhaseType.valuesFor(
                  bloc.details?.deviceType ?? AccessoriesDeviceType.ct3,
                  factoryStr: factoryStr),
            ),
          ),
        );
        if (context.mounted && value is ShellyCtPhaseType) {
          context.read<CtBloc>().add(SetCtPhaseEvent(value));
        }
      },
    );
  }

  /// 通道显示
  CardItemData _buildChannelItem(BuildContext context) {
    return CardItemData(
      title: S.current.text('device.ct_details_channel'),
      content: bloc.details?.channel?.name ?? '-',
      onTap: () async {
        if (bloc.details?.isOffline == true) return;
        var value = await Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => ShellyCtPhaseSelectPage<ShellyCtChannelType>(
              value: bloc.details?.channel,
              list: ShellyCtChannelType.values.toList(),
            ),
          ),
        );
        if (context.mounted && value is ShellyCtChannelType) {
          context.read<CtBloc>().add(SetCtChannelEvent(value));
        }
      },
    );
  }

  /// 相位反转
  CardItemData _buildPhaseReverseItem(BuildContext context) {
    return CardItemData(
      title: S.current.text('device.ct_details_phase_reverse'),
      onTap: () async {
        if (bloc.details?.isOffline == true) return;
        var res = await Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => PhaseReversePage(
              value: bloc.details!.phaseReverseBit,
              type: bloc.details!.deviceType,
            ),
          ),
        );
        if (res is List<int>) bloc.add(SetPhaseReverseEvent(res));
      },
    );
  }

  /// 设置低能耗模式
  _buildEcoModeItem(BuildContext context) {
    bool isOpen =
        bloc.details?.lowModeSetPropertyValue == LowModeSetPropertyValue.open;
    return CardItemData(
      title: S.current.text('device.ct_details_ECO_mode'),
      showIcon: false,
      child: Padding(
        padding: EdgeInsets.only(right: 10.w),
        child: CustomSwitch(
          value: isOpen,
          disabled: bloc.details?.isOffline == true,
          onToggle: (value) async {
            bloc.add(SetLowModeEvent(value
                ? LowModeSetPropertyValue.open
                : LowModeSetPropertyValue.close));
            await bloc.stream.firstWhere((event) =>
                event is GetCtDetailsSuccess || event is SetCtPhaseFailure);
          },
        ),
      ),
    );
  }

  _splitTitle(String i18nStr, int index) {
    var title = S.current.text(i18nStr);
    if (title.contains('/')) title = title.split('/')[index];
    return title;
  }
}
