import 'package:flutter/cupertino.dart';
import 'package:flutter_basic/features/trend/components/trend_line_chart_output.dart';
import 'package:flutter_basic/repositories/trend_repository/model/trend_common_model.dart';

class PowerTrend extends StatelessWidget {
  final List<TrendLineData> data;
  final double? minY;
  final double? maxY;

  const PowerTrend({super.key, required this.data, this.minY, this.maxY});

  @override
  Widget build(BuildContext context) {
    return TrendLineChartOutput(data: data, minY: minY, maxY: maxY);
  }
}
