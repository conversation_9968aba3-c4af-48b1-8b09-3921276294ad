import 'dart:math';

import 'package:flutter_basic/components/fl_chart/fl_chart.dart';
import 'package:flutter_basic/features/trend/components/trend_slider.dart';
import 'package:flutter_basic/generated/l10n.dart';
import 'package:flutter_basic/platform/platform.dart';
import 'package:flutter_basic/platform/utils/color_utils.dart';
import 'package:flutter_basic/repositories/trend_repository/model/trend_common_model.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../utils/TrendUtil.dart';
import 'lenged_widget.dart';

class TrendLineChartOutput extends StatefulWidget {
  final List<TrendLineData>? data;
  final double? minY;
  final double? maxY;

  const TrendLineChartOutput({super.key, this.data, this.minY, this.maxY});

  @override
  State createState() => TrendLineChartOutputState();
}

class TrendLineChartOutputState extends State<TrendLineChartOutput> {
  List<Color> gradientColors = [
    ColorsUtil.chargeLineColor,
    ColorsUtil.disChargeLineColor,
  ];

  var startPos = 0;
  var endPos = 100;
  var fractionDigits = 2;
  TrendLineData maxLineData = TrendLineData.empty();
  String? unit = '';
  List<TrendLineData> lineDatas = [];

  @override
  void initState() {
    super.initState();
  }

  List<TrendLineData> getLineData(List<TrendLineData>? data) {
    final datas = data ?? [];
    final lineDatas = <TrendLineData>[];
    maxLineData = TrendLineData.empty();
    for (TrendLineData data in datas) {
      final length = data.data.length;
      final start = (length * startPos) ~/ 100;
      final end = (length * endPos) ~/ 100;
      final subData = data.data.sublist(start, end);
      final trendLineData =
          TrendLineData(data: subData, name: data.name, color: data.color);
      lineDatas.add(trendLineData);
      if (maxLineData.data.length < subData.length) {
        maxLineData = trendLineData;
      }
    }
    return lineDatas;
  }

  List<String> getXAxis(List<String>? data) {
    final length = data?.length ?? 0;
    final start = (length * startPos) ~/ 100;
    final end = (length * endPos) ~/ 100;
    return (data ?? []).sublist(start, end);
  }

  @override
  Widget build(BuildContext context) {
    final data =
        widget.data != null && widget.data!.isNotEmpty ? widget.data![0] : null;
    unit = data?.unit;
    fractionDigits = data?.fractionDigits ?? 0;
    final legends = List.generate(
      widget.data?.length ?? 0,
      (index) => Legend(widget.data?[index].name ?? '',
          widget.data?[index].color ?? Colors.transparent),
    );
    return Stack(
      children: <Widget>[
        Column(
          children: [
            Padding(
              padding: EdgeInsets.only(
                top: 20.h,
                bottom: 20.h,
              ),
              child: Flex(
                direction: Axis.horizontal,
                children: [
                  Text(
                    unit != null
                        ? '${S.current.text('trend.unit')} ($unit)'
                        : '',
                    style: TextStyle(
                      fontSize: 10.sp,
                      color: ColorsUtil.chartUnitColor,
                    ),
                  ),
                  Expanded(
                    flex: 1,
                    child: Align(
                      alignment: Alignment.centerRight,
                      child: LegendsListWidget(
                        legends: legends,
                      ),
                    ),
                  )
                ],
              ),
            ),
            Padding(
              padding: EdgeInsets.only(right: 0.w),
              child: AspectRatio(
                aspectRatio: 1.7,
                child: LineChart(mainData()),
              ),
            ),
            TrendSlider(
              lowerValue: startPos.toDouble(),
              upperValue: endPos.toDouble(),
              onDragging:
                  (int handlerIndex, double lowerValue, double upperValue) {
                setState(() {
                  startPos = lowerValue.toInt();
                  endPos = upperValue.toInt();
                });
              },
            )
          ],
        ),
      ],
    );
  }

  double? getBottomInterval() {
    if (lineDatas.length > 8) {
      return 0.1;
    }
    if (lineDatas.length >= 12) {
      return 2.5;
    }
    return null;
  }

  Widget bottomTitleWidgets(double value, TitleMeta meta) {
    final style = TextStyle(
      color: ColorsUtil.chartLabelColor,
      fontSize: 9,
    );
    const angle = -45.0;
    if (lineDatas.isEmpty) {
      return SideTitleWidget(
        axisSide: meta.axisSide,
        angle: angle,
        child: Text('', style: style),
      );
    }
    final lineData = maxLineData.data;
    // final x = getXAxis(xAxis);
    if (lineData.isEmpty) {
      return SideTitleWidget(
        axisSide: meta.axisSide,
        child: const SizedBox(),
      );
    }

    // logger.d('trend___line___chart___value: ${value}___mate:$meta');
    if (value >= lineData.length) {
      return SideTitleWidget(
        axisSide: meta.axisSide,
        angle: angle,
        child: Text('', style: style),
      );
    }

    return SideTitleWidget(
      axisSide: meta.axisSide,
      angle: angle,
      child: Text(
          TimeUtils.formatTrendDayTime(lineData[value.toInt()].date ?? ''),
          style: style),
    );
  }

  Widget leftTitleWidgets(double value, TitleMeta meta) {
    final style = TextStyle(
      color: ColorsUtil.chartLabelColor,
      fontSize: 9.sp,
    );
    // final text = value < 10 ? value.toStringAsFixed(1) : value.toStringAsFixed(1) ;
    return Text(
      value.toStringAsFixed(fractionDigits),
      style: style,
      textAlign: TextAlign.center,
    );
  }

  Widget rightTitleWidgets(double value, TitleMeta meta) {
    final style = TextStyle(
      color: ColorsUtil.chartLabelColor,
      fontSize: 9.sp,
    );

    return Text(value.toString(), style: style, textAlign: TextAlign.right);
  }

  LineChartData mainData() {
    final lineData = widget.data != null && widget.data!.isNotEmpty
        ? widget.data![0]
        : TrendLineData(data: []);
    final color = lineData.color ?? ColorsUtil.chartLineColor1;
    lineDatas = getLineData(widget.data);
    final maxX = lineDatas.isEmpty ? null : lineDatas[0].data.length.toDouble();
    return LineChartData(
      maxX: maxX == null ? null : (maxX <= 24 ? maxX - 1 : maxX),
      minX: 0,
      minY: widget.minY ?? TrendUtil.getLineMin(lineDatas),
      maxY: max(widget.maxY ?? _getMaxY(), 4),
      lineTouchData: LineTouchData(
        getTouchLineEnd: (data, index) => double.infinity,
        getTouchedSpotIndicator:
            (LineChartBarData barData, List<int> spotIndexes) {
          return spotIndexes.map((spotIndex) {
            return TouchedSpotIndicatorData(
              FlLine(color: color, strokeWidth: 1, dashArray: [2, 4]),
              FlDotData(
                getDotPainter: (spot, percent, barData, index) =>
                    FlDotCirclePainter(radius: 0, color: color),
              ),
            );
          }).toList();
        },
        // touchSpotThreshold: 2,
        touchTooltipData: LineTouchTooltipData(
          tooltipBgColor: ColorsUtil.chartTooltipBgColor,
          maxContentWidth: 160.w,
          fitInsideVertically: true,
          fitInsideHorizontally: true,
          showOnTopOfTheChartBoxArea: true,
          getTooltipItems: (List<LineBarSpot> touchedBarSpots) {
            final textStyle = TextStyle(
              color: ColorsUtil.chartTooltipTextColor,
              fontSize: 10.sp,
            );
            var time = '';
            var x = 0;
            if (touchedBarSpots.isNotEmpty) {
              final barSpot = touchedBarSpots[0];
              x = barSpot.x.toInt();
              if (maxLineData.data.length > x) {
                time =
                    '${TimeUtils.formatTrendDayTime(maxLineData.data[barSpot.x.toInt()].date ?? '')}';
              }
            }
            return [
              LineTooltipItem(
                time,
                textStyle,
                textAlign: TextAlign.start,
              ),
              ...List.generate(
                lineDatas.length,
                (index) {
                  String description = '';
                  if (lineDatas[index].data[x].val! >= 0) {
                    description =
                        S.current.text("trend.output_power_ac_output");
                  } else {
                    description = S.current.text("trend.output_power_ac_input");
                  }
                  if (lineDatas[index].data.length <= x) {
                    return LineTooltipItem(
                        '${lineDatas[index].name != null ? '$description}: ' : ''}--$unit',
                        textStyle,
                        textAlign: TextAlign.start,
                        icon: TooltipIcon(color: lineDatas[index].color));
                  }
                  return LineTooltipItem(
                    '${lineDatas[index].name != null ? '$description: ' : ''}${lineDatas[index].data[x].displayValue() ?? '--'}$unit',
                    textStyle,
                    textAlign: TextAlign.start,
                    icon: TooltipIcon(color: lineDatas[index].color),
                  );
                },
              )
            ];
          },
        ),
      ),
      gridData: FlGridData(
        show: true,
        drawVerticalLine: false,
        // horizontalInterval: 1,
        // verticalInterval: 1,
        getDrawingHorizontalLine: (value) {
          return FlLine(
            color: ColorsUtil.chartLineColor,
            strokeWidth: 1,
          );
        },
      ),
      titlesData: FlTitlesData(
        show: true,
        rightTitles: AxisTitles(
          sideTitles: SideTitles(
            showTitles: false,
            getTitlesWidget: rightTitleWidgets,
          ),
        ),
        topTitles: const AxisTitles(
          sideTitles: SideTitles(showTitles: false),
        ),
        bottomTitles: AxisTitles(
          drawBelowEverything: false,
          sideTitles: SideTitles(
            showTitles: true,
            reservedSize: 45,
            interval: (maxX ?? 25) <= 24 ? 1 : null,
            getTitlesWidget: bottomTitleWidgets,
          ),
        ),
        leftTitles: AxisTitles(
          sideTitles: SideTitles(
            showTitles: true,
            getTitlesWidget: leftTitleWidgets,
            reservedSize: 40,
          ),
        ),
      ),
      borderData: FlBorderData(
        show: true,
        border: Border(
          bottom: BorderSide(
            color: ColorsUtil.chartLineColor,
          ),
        ),
      ),
      // minX: 0,
      // maxX: 11,
      // minY: -10,
      // maxY: 6,
      lineBarsData: _getLineBarsData(),
    );
  }

  List<LineChartBarData> _getLineBarsData() {
    return List.generate(
      lineDatas.length,
      (index) {
        return LineChartBarData(
          spots: _getData(lineDatas[index]),
          // isCurved: true,
          // gradient: LinearGradient(
          //   colors: gradientColors,
          // ),
          color: widget.data?[index].color ?? gradientColors[0],
          barWidth: 1,
          // isStrokeCapRound: true,
          dotData: const FlDotData(
            show: false,
          ),
          belowBarData: BarAreaData(
            show: true,
            color: widget.data?[index].color ?? gradientColors[0],
            gradient: LinearGradient(
              colors: [
                widget.data?[index].color?.withOpacity(0.15) ??
                    Colors.transparent,
                widget.data?[index].color?.withOpacity(0) ?? Colors.transparent
              ],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
            cutOffY: 0,
            applyCutOffY: true,
          ),
          aboveBarData: BarAreaData(
            show: true,
            color: widget.data?[index].color ?? gradientColors[1],
            gradient: LinearGradient(
              colors: [
                // widget.data?[index].color?.withOpacity(1) ?? Colors.transparent,
                // widget.data?[index].color?.withOpacity(0) ?? Colors.transparent
                widget.data?[index].color?.withOpacity(0.15) ??
                    Colors.transparent,
                widget.data?[index].color?.withOpacity(0) ?? Colors.transparent
              ],
              begin: Alignment.bottomCenter,
              end: Alignment.topCenter,
            ),
            cutOffY: 0,
            applyCutOffY: true,
          ),
          // belowBarData: BarAreaData(
          //   show: true,
          //   gradient: LinearGradient(
          //     colors: gradientColors.map((color) => color.withOpacity(0.3)).toList(),
          //   ),
          // ),
        );
      },
    );
  }

  List<FlSpot> _getData(TrendLineData lineData) {
    final data = lineData.data;
    return List.generate(
        data.length,
        (index) => data[index].val == null
            ? FlSpot.nullSpot
            : FlSpot(index.toDouble(), data[index].val!));
  }

  double _getMaxY() {
    final maxY = TrendUtil.getLineMax(lineDatas);
    final minY = TrendUtil.getLineMin(lineDatas);
    if (maxY == 0 && minY == 0) {
      return 100;
    }
    return maxY;
  }
}

class TooltipItem extends StatelessWidget {
  final String title;
  final Color color;

  const TooltipItem({super.key, required this.title, required this.color});

  @override
  Widget build(BuildContext context) {
    return LegendWidget(name: title, color: color);
  }
}
