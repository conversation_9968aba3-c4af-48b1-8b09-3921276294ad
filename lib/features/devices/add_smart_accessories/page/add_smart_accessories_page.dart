import 'package:flutter_basic/components/components.dart';
import 'package:flutter_basic/components/cus_InkWell/CusInkWell.dart';
import 'package:flutter_basic/components/custom_card.dart';
import 'package:flutter_basic/platform/platform.dart';
import 'package:flutter_basic/platform/utils/color_utils.dart';
import 'package:flutter_basic/router/devices_feature_routes/devices_routes.dart';
import 'package:flutter_basic/router/devices_feature_routes/devices_routes_type.dart';
import 'package:flutter_basic/router/diy_feature_routes/diy_route_type.dart';
import 'package:flutter_basic/router/diy_feature_routes/diy_routes.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class AddSmartAccessoriesPage extends StatefulWidget {
  final String? systemId;
  final String? systemNo;
  final bool clustered;

  const AddSmartAccessoriesPage({
    super.key,
    this.systemId,
    this.systemNo,
    this.clustered = false,
  });

  @override
  State<StatefulWidget> createState() => _AddSmartAccessoriesPageState();
}

class _AddSmartAccessoriesPageState extends State<AddSmartAccessoriesPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorsUtil.backgroundColor,
      appBar: CustomAppBar(
        title: const AppBarCenterText(
          title: 'Add Smart Accessories',
        ),
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(20.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Please add equipment according to the equipment classification below.',
              style: TextStyle(
                fontSize: 16.sp,
                color: ColorsUtil.textColor,
                fontWeight: FontWeight.w400,
              ),
            ),
            SizedBox(height: 20.h),
            _buildBluetoothSection(),
            SizedBox(height: 20.h),
            _buildLocalNetworkSection(),
          ],
        ),
      ),
    );
  }

  Widget _buildBluetoothSection() {
    return CustomCard(
      child: Padding(
        padding: EdgeInsets.all(20.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CustomImageAsset(
                  'icon_diy_bluetooth',
                  width: 24.w,
                  height: 24.w,
                ),
                SizedBox(width: 12.w),
                Text(
                  'Add via Bluetooth',
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: ColorsUtil.textColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            SizedBox(height: 12.h),
            Text(
              'Add via Bluetooth pairing. Supports the following device models:',
              style: TextStyle(
                fontSize: 14.sp,
                color: ColorsUtil.contentColor,
                fontWeight: FontWeight.w400,
              ),
            ),
            SizedBox(height: 16.h),
            _buildDeviceList([
              'Shelly Plug S Gen3',
              'Shelly Plug S',
              'Shelly Pro 3EM',
              'Shelly Pro 3EM',
            ]),
            SizedBox(height: 20.h),
            _buildActionButton(
              title: 'Add Such Devices',
              onTap: _navigateToAddDevices,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLocalNetworkSection() {
    return CustomCard(
      child: Padding(
        padding: EdgeInsets.all(20.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CustomImageAsset(
                  'icon_wifi_router',
                  width: 24.w,
                  height: 24.w,
                ),
                SizedBox(width: 12.w),
                Text(
                  'Add via Local Area Network',
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: ColorsUtil.textColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            SizedBox(height: 12.h),
            Text(
              'The device is connected to the home network and can be added via the local area network. It supports the following device models:',
              style: TextStyle(
                fontSize: 14.sp,
                color: ColorsUtil.contentColor,
                fontWeight: FontWeight.w400,
              ),
            ),
            SizedBox(height: 16.h),
            _buildNetworkDeviceItem(
              title: 'EverHome EcoTracker',
              onTap: _navigateToEverHomeEcotracker,
            ),
            _buildNetworkDeviceItem(
              title: 'Jackery Honey Energy Dongle Name',
              onTap: _navigateToJackeryHoney,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDeviceList(List<String> devices) {
    return Container(
      decoration: BoxDecoration(
        color: const Color(0xFFF5F5F5), // 灰色背景
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Column(
        children: devices.asMap().entries.map((entry) {
          int index = entry.key;
          String device = entry.value;
          bool isLast = index == devices.length - 1;

          return Column(
            children: [
              Container(
                padding: EdgeInsets.symmetric(vertical: 12.h, horizontal: 16.w),
                child: Row(
                  children: [
                    // Container(
                    //   width: 4.w,
                    //   height: 4.w,
                    //   decoration: BoxDecoration(
                    //     color: ColorsUtil.contentColor,
                    //     shape: BoxShape.circle,
                    //   ),
                    // ),
                    SizedBox(width: 12.w),
                    Text(
                      device,
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: ColorsUtil.textColor,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ],
                ),
              ),
              if (!isLast)
                Container(
                  height: 1.h,
                  margin: EdgeInsets.symmetric(horizontal: 16.w),
                  color: const Color(0xFFE0E0E0), // 分割线颜色
                ),
            ],
          );
        }).toList(),
      ),
    );
  }

  Widget _buildActionButton({
    required String title,
    required VoidCallback onTap,
  }) {
    return CusInkWell(
      onTap: onTap,
      child: Container(
        width: double.infinity,
        height: 44.h,
        decoration: BoxDecoration(
          color: ColorsUtil.themeColor,
          borderRadius: BorderRadius.circular(22.r),
        ),
        alignment: Alignment.center,
        child: Text(
          title,
          style: TextStyle(
            fontSize: 16.sp,
            color: Colors.white,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }

  Widget _buildNetworkDeviceItem({
    required String title,
    required VoidCallback onTap,
  }) {
    return Container(
      margin: EdgeInsets.only(bottom: 12.h),
      decoration: BoxDecoration(
        color: const Color(0xFFF5F5F5), // 灰色背景
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: CusInkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8.r),
        child: Container(
          padding: EdgeInsets.symmetric(vertical: 16.h, horizontal: 16.w),
          child: Row(
            children: [
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: ColorsUtil.textColor,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ),
              Container(
                padding: EdgeInsets.symmetric(vertical: 6.h, horizontal: 12.w),
                decoration: BoxDecoration(
                  color: ColorsUtil.themeColor,
                  borderRadius: BorderRadius.circular(15.r),
                ),
                child: Text(
                  'Add',
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: Colors.white,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _navigateToAddDevices() {
    if (widget.systemNo == null || widget.systemId == null) {
      CustomToast.showToast(context, 'System information is missing');
      return;
    }

    Navigator.of(context).push(
      DiyRoutes.onGenerateRoute(
        RouteSettings(
          name: DiyRouteType.diyAddShellyRoute,
          arguments: {
            'systemNo': widget.systemNo!,
            'systemId': widget.systemId!,
            'status': 1,
            'clustered': widget.clustered,
          },
        ),
      ),
    );
  }

  void _navigateToEverHomeEcotracker() {
    Navigator.of(context).push(
      IntergratedRoutes.onGenerateRoute(
        const RouteSettings(
          name: IntergratedRoutesType.everHomeEcotracker,
        ),
      ),
    );
  }

  void _navigateToJackeryHoney() {
    // TODO: Implement Jackery Honey navigation
    CustomToast.showToast(context, 'Feature coming soon');
  }
}
